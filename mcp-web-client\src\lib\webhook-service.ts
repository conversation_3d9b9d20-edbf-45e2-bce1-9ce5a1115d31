import {
  OpenAIResponsesRequest,
  OpenAIResponsesResponse,
  OpenAIMessage,
  OpenAITool,
  Tool,
  ChatMessage,
  ChatContent,
  OpenAIContent
} from '@/types/mcp';

export class WebhookService {
  private readonly webhookUrl = 'https://panel.asistentesautonomos.com/webhook/6f08dbcf-5888-4003-a386-766deb9976c8';

  /**
   * Formatea un ID para que cumpla con los requisitos de OpenAI
   */
  private formatMessageId(id: string): string {
    // Los IDs de mensajes deben comenzar con 'msg_'
    if (id.startsWith('msg_')) {
      return id;
    }
    return `msg_${id.replace(/-/g, '')}`;
  }

  /**
   * Formatea un ID para function calls
   */
  private formatFunctionCallId(id: string): string {
    // Los IDs de function calls pueden usar 'call_' o similar
    if (id.startsWith('call_')) {
      return id;
    }
    return `call_${id.replace(/-/g, '')}`;
  }

  /**
   * Convierte herramientas MCP al formato OpenAI Tools
   */
  private convertMCPToolsToOpenAI(mcpTools: Tool[]): OpenAITool[] {
    return mcpTools.map(tool => {
      console.log(`Procesando herramienta MCP: ${tool.name}`);
      console.log('Esquema original:', JSON.stringify(tool.inputSchema, null, 2));

      const properties = tool.inputSchema.properties || {};
      const originalRequired = tool.inputSchema.required || [];

      // Incluir todas las propiedades, pero marcar las opcionales con valores por defecto
      const allProperties: Record<string, any> = {};
      const allRequired: string[] = [];

      for (const [propName, propSchema] of Object.entries(properties)) {
        if (originalRequired.includes(propName)) {
          // Propiedad requerida - incluir tal como está
          allProperties[propName] = propSchema;
          allRequired.push(propName);
        } else {
          // Propiedad opcional - añadir valor por defecto y marcar como requerida
          allProperties[propName] = {
            ...propSchema,
            default: this.getDefaultValueForType(propSchema.type || 'string')
          };
          allRequired.push(propName);
        }
      }

      // Si no hay propiedades, crear un esquema vacío válido
      if (Object.keys(allProperties).length === 0) {
        const emptySchema = {
          type: 'function',
          description: tool.description || '',
          name: tool.name,
          parameters: {
            type: 'object',
            properties: {},
            required: [],
            additionalProperties: false
          },
          strict: true
        };
        console.log(`Esquema OpenAI para ${tool.name} (vacío):`, JSON.stringify(emptySchema, null, 2));
        return emptySchema;
      }

      const openAISchema = {
        type: 'function',
        description: tool.description || '',
        name: tool.name,
        parameters: {
          type: 'object',
          required: allRequired,
          properties: allProperties,
          additionalProperties: false
        },
        strict: true
      };

      console.log(`Esquema OpenAI para ${tool.name}:`, JSON.stringify(openAISchema, null, 2));
      return openAISchema;
    });
  }

  /**
   * Obtiene un valor por defecto apropiado según el tipo
   */
  private getDefaultValueForType(type: string): any {
    switch (type) {
      case 'string':
        return '';
      case 'number':
      case 'integer':
        return 0;
      case 'boolean':
        return false;
      case 'array':
        return [];
      case 'object':
        return {};
      default:
        return null;
    }
  }

  /**
   * Convierte mensajes de chat al formato OpenAI Messages
   */
  private convertChatMessagesToOpenAI(messages: ChatMessage[]): OpenAIMessage[] {
    const openAIMessages: OpenAIMessage[] = [];

    for (const message of messages) {
      if (message.role === 'user' || message.role === 'system' || message.role === 'assistant') {
        // Mensaje normal
        const content: OpenAIContent[] = message.content
          .filter(c => c.type === 'input_text' || c.type === 'output_text' || c.type === 'input_image')
          .map(c => ({
            type: c.type as 'input_text' | 'output_text' | 'input_image',
            text: c.text,
            image_url: c.image_url
          }));

        if (content.length > 0) {
          openAIMessages.push({
            role: message.role,
            content,
            id: this.formatMessageId(message.id)
          });
        }

        // Function calls como mensajes separados
        const functionCalls = message.content.filter(c => c.type === 'function_call');
        for (const fc of functionCalls) {
          openAIMessages.push({
            type: 'function_call',
            id: this.formatFunctionCallId(fc.id!),
            call_id: this.formatFunctionCallId(fc.call_id!),
            name: fc.name!,
            arguments: fc.arguments!
          } as any);
        }

        // Function call outputs como mensajes separados
        const functionOutputs = message.content.filter(c => c.type === 'function_call_output');
        for (const fo of functionOutputs) {
          openAIMessages.push({
            type: 'function_call_output',
            call_id: this.formatFunctionCallId(fo.call_id!),
            output: fo.output!
          } as any);
        }
      }
    }

    return openAIMessages;
  }

  /**
   * Envía un mensaje al webhook y obtiene la respuesta
   */
  async sendMessage(
    messages: ChatMessage[],
    availableTools: Tool[],
    model: string = 'gpt-4o'
  ): Promise<OpenAIResponsesResponse> {
    try {
      const openAIMessages = this.convertChatMessagesToOpenAI(messages);
      const openAITools = this.convertMCPToolsToOpenAI(availableTools);

      const request: OpenAIResponsesRequest = {
        model,
        input: openAIMessages,
        tools: openAITools.length > 0 ? openAITools : undefined,
        text: {
          format: {
            type: 'text'
          }
        },
        reasoning: {},
        temperature: 1,
        max_output_tokens: 2048,
        top_p: 1,
        store: true
      };

      console.log('Enviando request al webhook:', JSON.stringify(request, null, 2));

      const response = await fetch(this.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: OpenAIResponsesResponse = await response.json();
      console.log('Respuesta del webhook:', JSON.stringify(data, null, 2));

      return data;
    } catch (error) {
      console.error('Error enviando mensaje al webhook:', error);
      throw error;
    }
  }

  /**
   * Convierte la respuesta de OpenAI a formato de mensaje de chat
   */
  convertOpenAIResponseToChatMessage(
    response: OpenAIResponsesResponse,
    connectionId?: string
  ): ChatMessage {
    const content: ChatContent[] = [];

    // Procesar output items
    for (const item of response.output) {
      if (item.type === 'output_text' && item.text) {
        content.push({
          type: 'output_text',
          text: item.text
        });
      } else if (item.type === 'function_call') {
        content.push({
          type: 'function_call',
          id: item.id,
          call_id: item.call_id,
          name: item.name,
          arguments: item.arguments
        });
      }
    }

    // Si hay output_text directo, usarlo
    if (response.output_text && !content.some(c => c.type === 'output_text')) {
      content.push({
        type: 'output_text',
        text: response.output_text
      });
    }

    return {
      id: this.formatMessageId(response.id),
      role: 'assistant',
      content,
      timestamp: new Date(),
      connectionId
    };
  }

  /**
   * Extrae function calls de la respuesta de OpenAI
   */
  extractFunctionCalls(response: OpenAIResponsesResponse): Array<{
    id: string;
    call_id: string;
    name: string;
    arguments: string;
  }> {
    return response.output
      .filter(item => item.type === 'function_call')
      .map(item => ({
        id: this.formatFunctionCallId(item.id!),
        call_id: this.formatFunctionCallId(item.call_id!),
        name: item.name!,
        arguments: item.arguments!
      }));
  }

  /**
   * Crea un mensaje con el resultado de una function call
   */
  createFunctionCallOutputMessage(
    callId: string,
    output: string,
    connectionId?: string
  ): ChatMessage {
    return {
      id: this.formatMessageId(`func_output_${Date.now()}`),
      role: 'assistant',
      content: [{
        type: 'function_call_output',
        call_id: this.formatFunctionCallId(callId),
        output
      }],
      timestamp: new Date(),
      connectionId
    };
  }
}

export const webhookService = new WebhookService();
