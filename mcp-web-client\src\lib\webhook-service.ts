import {
  OpenAIResponsesRequest,
  OpenAIResponsesResponse,
  OpenAIMessage,
  OpenAITool,
  Tool,
  ChatMessage,
  ChatContent,
  OpenAIContent
} from '@/types/mcp';

export class WebhookService {
  private readonly webhookUrl = 'https://panel.asistentesautonomos.com/webhook/6f08dbcf-5888-4003-a386-766deb9976c8';

  /**
   * Formatea un ID para que cumpla con los requisitos de OpenAI
   */
  private formatMessageId(id: string): string {
    // Los IDs de mensajes deben comenzar con 'msg_'
    if (id.startsWith('msg_')) {
      return id;
    }
    return `msg_${id.replace(/-/g, '')}`;
  }

  /**
   * Formatea un ID para function calls
   */
  private formatFunctionCallId(id: string): string {
    // Los IDs de function calls pueden usar 'call_' o similar
    if (id.startsWith('call_')) {
      return id;
    }
    return `call_${id.replace(/-/g, '')}`;
  }

  /**
   * Convierte herramientas MCP al formato OpenAI Tools
   */
  private convertMCPToolsToOpenAI(mcpTools: Tool[]): OpenAITool[] {
    return mcpTools.map(tool => {
      console.log(`Procesando herramienta MCP: ${tool.name}`);
      console.log('Esquema original:', JSON.stringify(tool.inputSchema, null, 2));

      const processedSchema = this.processSchemaForOpenAI(tool.inputSchema);

      // Si no hay propiedades, crear un esquema vacío válido
      if (!processedSchema.properties || Object.keys(processedSchema.properties).length === 0) {
        const emptySchema = {
          type: 'function',
          description: tool.description || '',
          name: tool.name,
          parameters: {
            type: 'object',
            properties: {},
            required: [],
            additionalProperties: false
          },
          strict: true
        };
        console.log(`Esquema OpenAI para ${tool.name} (vacío):`, JSON.stringify(emptySchema, null, 2));
        return emptySchema;
      }

      const openAISchema = {
        type: 'function',
        description: tool.description || '',
        name: tool.name,
        parameters: {
          ...processedSchema,
          additionalProperties: false
        },
        strict: true
      };

      console.log(`Esquema OpenAI para ${tool.name}:`, JSON.stringify(openAISchema, null, 2));
      return openAISchema;
    });
  }

  /**
   * Procesa recursivamente un esquema para cumplir con los requisitos de OpenAI
   */
  private processSchemaForOpenAI(schema: any): any {
    if (!schema || typeof schema !== 'object') {
      return schema;
    }

    const result = { ...schema };

    // Si es un objeto con propiedades, procesar según las reglas de OpenAI
    if (result.type === 'object' && result.properties) {
      const properties = result.properties;
      const originalRequired = result.required || [];

      // Procesar cada propiedad recursivamente
      const processedProperties: Record<string, any> = {};
      const allRequired: string[] = [];

      for (const [propName, propSchema] of Object.entries(properties)) {
        // Procesar recursivamente la propiedad
        const processedProp = this.processSchemaForOpenAI(propSchema);

        if (originalRequired.includes(propName)) {
          // Propiedad requerida - incluir tal como está
          processedProperties[propName] = processedProp;
          allRequired.push(propName);
        } else {
          // Propiedad opcional - añadir valor por defecto y marcar como requerida
          processedProperties[propName] = {
            ...processedProp,
            default: this.getDefaultValueForType(processedProp.type || 'string')
          };
          allRequired.push(propName);
        }
      }

      result.properties = processedProperties;
      result.required = allRequired;
    }

    // Si es un array, procesar los items
    if (result.type === 'array' && result.items) {
      result.items = this.processSchemaForOpenAI(result.items);
    }

    // Si tiene anyOf, oneOf, etc., procesarlos también
    if (result.anyOf) {
      result.anyOf = result.anyOf.map((item: any) => this.processSchemaForOpenAI(item));
    }

    if (result.oneOf) {
      result.oneOf = result.oneOf.map((item: any) => this.processSchemaForOpenAI(item));
    }

    return result;
  }

  /**
   * Obtiene un valor por defecto apropiado según el tipo
   */
  private getDefaultValueForType(type: string): any {
    switch (type) {
      case 'string':
        return '';
      case 'number':
      case 'integer':
        return 0;
      case 'boolean':
        return false;
      case 'array':
        return [];
      case 'object':
        return {};
      default:
        return null;
    }
  }

  /**
   * Convierte mensajes de chat al formato OpenAI Messages
   */
  private convertChatMessagesToOpenAI(messages: ChatMessage[]): OpenAIMessage[] {
    const openAIMessages: OpenAIMessage[] = [];

    for (const message of messages) {
      if (message.role === 'user' || message.role === 'system' || message.role === 'assistant') {
        // Mensaje normal - NO incluir ID en mensajes de entrada
        const content: OpenAIContent[] = message.content
          .filter(c => c.type === 'input_text' || c.type === 'output_text' || c.type === 'input_image')
          .map(c => ({
            type: c.type as 'input_text' | 'output_text' | 'input_image',
            text: c.text,
            image_url: c.image_url
          }));

        if (content.length > 0) {
          const openAIMessage: OpenAIMessage = {
            role: message.role,
            content
          };

          // Solo incluir ID si es un mensaje de respuesta (assistant) que ya tiene un ID válido de OpenAI
          if (message.role === 'assistant' && message.id.startsWith('msg_')) {
            openAIMessage.id = message.id;
          }

          openAIMessages.push(openAIMessage);
        }

        // Function calls como mensajes separados
        const functionCalls = message.content.filter(c => c.type === 'function_call');
        for (const fc of functionCalls) {
          openAIMessages.push({
            type: 'function_call',
            id: this.formatFunctionCallId(fc.id!),
            call_id: this.formatFunctionCallId(fc.call_id!),
            name: fc.name!,
            arguments: fc.arguments!
          } as any);
        }

        // Function call outputs como mensajes separados
        const functionOutputs = message.content.filter(c => c.type === 'function_call_output');
        for (const fo of functionOutputs) {
          openAIMessages.push({
            type: 'function_call_output',
            call_id: this.formatFunctionCallId(fo.call_id!),
            output: fo.output!
          } as any);
        }
      }
    }

    return openAIMessages;
  }

  /**
   * Envía un mensaje al webhook y obtiene la respuesta
   */
  async sendMessage(
    messages: ChatMessage[],
    availableTools: Tool[],
    model: string = 'gpt-4o'
  ): Promise<OpenAIResponsesResponse> {
    try {
      const openAIMessages = this.convertChatMessagesToOpenAI(messages);
      const openAITools = this.convertMCPToolsToOpenAI(availableTools);

      const request: OpenAIResponsesRequest = {
        model,
        input: openAIMessages,
        tools: openAITools.length > 0 ? openAITools : undefined,
        text: {
          format: {
            type: 'text'
          }
        },
        reasoning: {},
        temperature: 1,
        max_output_tokens: 2048,
        top_p: 1,
        store: true
      };

      console.log('Enviando request al webhook:', JSON.stringify(request, null, 2));

      const response = await fetch(this.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: any = await response.json();
      console.log('Respuesta completa del webhook:', JSON.stringify(data, null, 2));
      console.log('Tipo de respuesta:', typeof data);
      console.log('Propiedades de la respuesta:', Object.keys(data));

      // Verificar si la respuesta tiene el formato esperado
      if (data.output) {
        console.log('Respuesta tiene output:', data.output);
        return data as OpenAIResponsesResponse;
      } else if (data.choices && data.choices[0] && data.choices[0].message) {
        // Parece ser una respuesta de Chat Completions API, convertir al formato Responses API
        console.log('Convirtiendo respuesta de Chat Completions a Responses API');
        const chatResponse = data;
        const convertedResponse: OpenAIResponsesResponse = {
          id: chatResponse.id || `response_${Date.now()}`,
          model: chatResponse.model || 'gpt-4o',
          output: [{
            type: 'output_text',
            text: chatResponse.choices[0].message.content
          }],
          output_text: chatResponse.choices[0].message.content,
          usage: chatResponse.usage
        };
        console.log('Respuesta convertida:', JSON.stringify(convertedResponse, null, 2));
        return convertedResponse;
      } else {
        console.error('Formato de respuesta no reconocido:', data);
        throw new Error('Formato de respuesta no válido');
      }

      return data;
    } catch (error) {
      console.error('Error enviando mensaje al webhook:', error);
      throw error;
    }
  }

  /**
   * Convierte la respuesta de OpenAI a formato de mensaje de chat
   */
  convertOpenAIResponseToChatMessage(
    response: OpenAIResponsesResponse,
    connectionId?: string
  ): ChatMessage {
    console.log('=== CONVERTIR RESPUESTA A MENSAJE ===');
    console.log('Response completa:', JSON.stringify(response, null, 2));
    console.log('Response.output:', response.output);
    console.log('Response.output_text:', response.output_text);

    const content: ChatContent[] = [];

    // Procesar output items
    if (response.output && Array.isArray(response.output)) {
      console.log('Procesando output items:', response.output.length);
      for (const item of response.output) {
        console.log('Procesando item:', item);
        if (item.type === 'output_text' && item.text) {
          console.log('Añadiendo output_text:', item.text);
          content.push({
            type: 'output_text',
            text: item.text
          });
        } else if (item.type === 'function_call') {
          console.log('Añadiendo function_call:', item);
          content.push({
            type: 'function_call',
            id: item.id,
            call_id: item.call_id,
            name: item.name,
            arguments: item.arguments
          });
        } else {
          console.log('Item no procesado:', item);
        }
      }
    } else {
      console.log('No hay output array o no es array');
    }

    // Si hay output_text directo, usarlo
    if (response.output_text && !content.some(c => c.type === 'output_text')) {
      console.log('Usando output_text directo:', response.output_text);
      content.push({
        type: 'output_text',
        text: response.output_text
      });
    }

    console.log('Content final:', content);

    const finalMessage = {
      id: response.id, // Usar el ID que devuelve OpenAI directamente
      role: 'assistant' as const,
      content,
      timestamp: new Date(),
      connectionId
    };

    console.log('Mensaje final creado:', finalMessage);
    return finalMessage;
  }

  /**
   * Extrae function calls de la respuesta de OpenAI
   */
  extractFunctionCalls(response: OpenAIResponsesResponse): Array<{
    id: string;
    call_id: string;
    name: string;
    arguments: string;
  }> {
    return response.output
      .filter(item => item.type === 'function_call')
      .map(item => ({
        id: this.formatFunctionCallId(item.id!),
        call_id: this.formatFunctionCallId(item.call_id!),
        name: item.name!,
        arguments: item.arguments!
      }));
  }

  /**
   * Crea un mensaje con el resultado de una function call
   */
  createFunctionCallOutputMessage(
    callId: string,
    output: string,
    connectionId?: string
  ): ChatMessage {
    return {
      id: `func_output_${Date.now()}`, // ID temporal, se enviará sin ID a OpenAI
      role: 'assistant',
      content: [{
        type: 'function_call_output',
        call_id: callId, // Usar el call_id tal como viene
        output
      }],
      timestamp: new Date(),
      connectionId
    };
  }
}

export const webhookService = new WebhookService();
