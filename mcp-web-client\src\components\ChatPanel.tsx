'use client';

import React, { useState, useRef, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeSanitize from 'rehype-sanitize';
import rehypeHighlight from 'rehype-highlight';
import { useMCPClientContext } from './MCPClientProvider';
import { ChatMessage, ChatContent, CallToolRequest } from '@/types/mcp';
import { webhookService } from '@/lib/webhook-service';
import { v4 as uuidv4 } from 'uuid';

export function ChatPanel() {
  const { getActiveConnection, callTool } = useMCPClientContext();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const activeConnection = getActiveConnection();
  const availableTools = activeConnection?.tools || [];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addSystemMessage = () => {
    const systemMessage: ChatMessage = {
      id: `msg_${uuidv4().replace(/-/g, '')}`,
      role: 'system',
      content: [{
        type: 'input_text',
        text: 'Responde de manera siempre educada, profesional y amable. Tienes acceso a herramientas MCP que puedes usar para ayudar al usuario.'
      }],
      timestamp: new Date(),
      connectionId: activeConnection?.id
    };
    setMessages([systemMessage]);
  };

  useEffect(() => {
    if (messages.length === 0) {
      addSystemMessage();
    }
  }, [activeConnection?.id]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || !activeConnection) return;

    const userMessage: ChatMessage = {
      id: `msg_${uuidv4().replace(/-/g, '')}`,
      role: 'user',
      content: [{
        type: 'input_text',
        text: inputMessage.trim()
      }],
      timestamp: new Date(),
      connectionId: activeConnection.id
    };

    const newMessages = [...messages, userMessage];
    setMessages(newMessages);
    setInputMessage('');
    setIsLoading(true);
    setError(null);

    try {
      console.log('Enviando mensajes al webhook:', newMessages);
      console.log('Herramientas disponibles:', availableTools);

      // Enviar al webhook
      const response = await webhookService.sendMessage(
        newMessages,
        availableTools,
        'gpt-4o'
      );

      console.log('Respuesta del webhook recibida:', response);

      // Convertir respuesta a mensaje de chat
      const assistantMessage = webhookService.convertOpenAIResponseToChatMessage(
        response,
        activeConnection.id
      );

      // Verificar si hay function calls
      const functionCalls = webhookService.extractFunctionCalls(response);

      if (functionCalls.length > 0) {
        console.log('Function calls detectados:', functionCalls);
        // Ejecutar function calls
        const updatedMessages = [...newMessages, assistantMessage];
        await executeFunctionCalls(functionCalls, updatedMessages);
      } else {
        // Solo agregar la respuesta del asistente
        setMessages([...newMessages, assistantMessage]);
      }
    } catch (err) {
      console.error('Error en handleSendMessage:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  const executeFunctionCalls = async (
    functionCalls: Array<{ id: string; call_id: string; name: string; arguments: string }>,
    currentMessages: ChatMessage[]
  ) => {
    const functionOutputMessages: ChatMessage[] = [];

    for (const fc of functionCalls) {
      try {
        // Parsear argumentos
        const args = JSON.parse(fc.arguments);
        
        // Crear request para MCP
        const toolRequest: CallToolRequest = {
          name: fc.name,
          arguments: args
        };

        // Ejecutar herramienta MCP
        const toolResult = await callTool(activeConnection!.id, toolRequest);
        
        // Convertir resultado a string
        const outputText = toolResult.content
          .map(content => {
            if (content.type === 'text') return content.text;
            if (content.type === 'image') return `[Imagen: ${content.mimeType}]`;
            if (content.type === 'resource') return `[Recurso: ${content.text}]`;
            return '';
          })
          .join('\n');

        // Crear mensaje con el output
        const outputMessage = webhookService.createFunctionCallOutputMessage(
          fc.call_id,
          outputText,
          activeConnection!.id
        );

        functionOutputMessages.push(outputMessage);
      } catch (err) {
        // Crear mensaje de error
        const errorMessage = webhookService.createFunctionCallOutputMessage(
          fc.call_id,
          `Error ejecutando ${fc.name}: ${err instanceof Error ? err.message : 'Error desconocido'}`,
          activeConnection!.id
        );
        functionOutputMessages.push(errorMessage);
      }
    }

    // Agregar todos los outputs y enviar de nuevo al webhook
    const messagesWithOutputs = [...currentMessages, ...functionOutputMessages];
    setMessages(messagesWithOutputs);

    try {
      // Enviar de nuevo al webhook con los resultados
      const finalResponse = await webhookService.sendMessage(
        messagesWithOutputs,
        availableTools,
        'gpt-4o'
      );

      const finalMessage = webhookService.convertOpenAIResponseToChatMessage(
        finalResponse,
        activeConnection!.id
      );

      setMessages([...messagesWithOutputs, finalMessage]);
    } catch (err) {
      setError(`Error obteniendo respuesta final: ${err instanceof Error ? err.message : 'Error desconocido'}`);
    }
  };

  const renderMessageContent = (content: ChatContent) => {
    switch (content.type) {
      case 'input_text':
      case 'output_text':
        return (
          <div className="prose prose-sm max-w-none dark:prose-invert">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeSanitize, rehypeHighlight]}
              components={{
                code: ({ inline, className, children, ...props }) => {
                  const match = /language-(\w+)/.exec(className || '');
                  return !inline && match ? (
                    <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg overflow-x-auto">
                      <code className={className} {...props}>
                        {children}
                      </code>
                    </pre>
                  ) : (
                    <code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm" {...props}>
                      {children}
                    </code>
                  );
                },
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-600 dark:text-gray-300">
                    {children}
                  </blockquote>
                ),
                table: ({ children }) => (
                  <div className="overflow-x-auto">
                    <table className="min-w-full border border-gray-300 dark:border-gray-600">
                      {children}
                    </table>
                  </div>
                ),
                th: ({ children }) => (
                  <th className="border border-gray-300 dark:border-gray-600 px-3 py-2 bg-gray-100 dark:bg-gray-700 font-semibold">
                    {children}
                  </th>
                ),
                td: ({ children }) => (
                  <td className="border border-gray-300 dark:border-gray-600 px-3 py-2">
                    {children}
                  </td>
                )
              }}
            >
              {content.text || ''}
            </ReactMarkdown>
          </div>
        );
      
      case 'input_image':
        return (
          <img 
            src={content.image_url} 
            alt="Imagen del usuario" 
            className="max-w-sm rounded-lg"
          />
        );
      
      case 'function_call':
        return (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-2">
              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span className="text-sm font-medium text-blue-800 dark:text-blue-300">
                Ejecutando: {content.name}
              </span>
            </div>
            <pre className="text-xs text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/40 p-2 rounded overflow-x-auto">
              {JSON.stringify(JSON.parse(content.arguments || '{}'), null, 2)}
            </pre>
          </div>
        );
      
      case 'function_call_output':
        return (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-2">
              <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm font-medium text-green-800 dark:text-green-300">
                Resultado de herramienta
              </span>
            </div>
            <pre className="text-sm text-green-700 dark:text-green-300 whitespace-pre-wrap">
              {content.output}
            </pre>
          </div>
        );
      
      default:
        return null;
    }
  };



  if (!activeConnection) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">
          Selecciona una conexión activa para usar el chat.
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-[600px]">
      {/* Header */}
      <div className="border-b border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Chat con IA
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Conexión: {activeConnection.name} • {availableTools.length} herramientas disponibles
        </p>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.filter(m => m.role !== 'system').map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg p-3 ${
                message.role === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
              }`}
            >
              <div className="space-y-2">
                {message.content.map((content, index) => (
                  <div key={index}>
                    {renderMessageContent(content)}
                  </div>
                ))}
              </div>
              <div className="text-xs opacity-70 mt-2">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-sm text-gray-600 dark:text-gray-300">
                  Pensando...
                </span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Error */}
      {error && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border-t border-red-200 dark:border-red-800">
          <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
        </div>
      )}

      {/* Input */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-4">
        <div className="flex space-x-2">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && handleSendMessage()}
            placeholder="Escribe tu mensaje..."
            className="flex-1 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={isLoading || !inputMessage.trim()}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Enviar
          </button>
        </div>
      </div>
    </div>
  );
}
